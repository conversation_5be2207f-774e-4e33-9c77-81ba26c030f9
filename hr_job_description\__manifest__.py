# -*- coding: utf-8 -*-
{
    'name': 'HR Job Description',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Professional Job Description Management for Employees',
    'description': """
HR Job Description Module
=========================

This module adds a comprehensive Job Description tab to the employee form in HR module.
The tab contains detailed information about the employee's job description following
international HR best practices.

Features:
---------
* Professional Job Description tab next to HR Settings
* Comprehensive job information fields
* Modern UI design following Odoo 15 standards
* Bilingual support (Arabic/English)
* Proper access rights and security
* Compatible with Odoo 15 official UI/UX patterns
* Follows HR best practices for job descriptions

Job Description Fields:
----------------------
- Job Title (العنوان الوظيفي)
- Department (القسم)
- Main Responsibilities (المسؤوليات الرئيسية)
- Required Qualifications (المؤهلات المطلوبة)
- Skills & Experience (المهارات والخبرات)
- Reports To (تقارير إلى)
- Job Level (المستوى الوظيفي)
- Work Location (موقع العمل)
- Last Update Date (تاريخ آخر تحديث)

Author: Custom Development
    """,
    'author': 'Custom Development',
    'website': '',
    'depends': ['hr', 'mail'],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_employee_views.xml',
    ],
    'qweb': [],
    'demo': [
        'demo/hr_job_description_demo.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XML validation script for HR Job Description Module
"""

import xml.etree.ElementTree as ET
import os

def validate_xml_file(file_path):
    """Validate XML file"""
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        print(f"✅ {file_path} is valid XML")
        return True
    except ET.ParseError as e:
        print(f"❌ {file_path} has XML errors: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 Validating XML files...")
    print("=" * 40)
    
    xml_files = [
        'views/hr_employee_views.xml',
        'demo/hr_job_description_demo.xml'
    ]
    
    all_valid = True
    for xml_file in xml_files:
        if os.path.exists(xml_file):
            if not validate_xml_file(xml_file):
                all_valid = False
        else:
            print(f"❌ File not found: {xml_file}")
            all_valid = False
    
    print("=" * 40)
    if all_valid:
        print("🎉 All XML files are valid!")
    else:
        print("❌ Some XML files have errors!")
    
    return all_valid

if __name__ == "__main__":
    main()

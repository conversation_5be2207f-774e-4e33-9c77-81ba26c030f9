#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final verification script for HR Job Description Module
"""

import os
import xml.etree.ElementTree as ET

def check_manifest():
    """Check manifest file"""
    print("🔍 Checking __manifest__.py...")
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check required fields
        required_fields = [
            "'name':", "'version':", "'category':", "'summary':",
            "'depends':", "'data':", "'installable': True"
        ]
        
        for field in required_fields:
            if field not in content:
                print(f"   ❌ Missing: {field}")
                return False
        
        print("   ✅ Manifest file is valid")
        return True
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def check_models():
    """Check model files"""
    print("🔍 Checking model files...")
    
    # Check hr_job_description.py
    try:
        with open('models/hr_job_description.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_elements = [
            "class HrJobDescription(models.Model):",
            "_name = 'hr.job.description'",
            "active = fields.Boolean",
            "job_title = fields.Char",
            "employee_id = fields.Many2one"
        ]
        
        for element in required_elements:
            if element not in content:
                print(f"   ❌ Missing in hr_job_description.py: {element}")
                return False
        
        print("   ✅ hr_job_description.py is valid")
    except Exception as e:
        print(f"   ❌ Error in hr_job_description.py: {e}")
        return False
    
    # Check hr_employee.py
    try:
        with open('models/hr_employee.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_elements = [
            "class HrEmployee(models.Model):",
            "_inherit = 'hr.employee'",
            "job_description_ids = fields.One2many"
        ]
        
        for element in required_elements:
            if element not in content:
                print(f"   ❌ Missing in hr_employee.py: {element}")
                return False
        
        print("   ✅ hr_employee.py is valid")
        return True
    except Exception as e:
        print(f"   ❌ Error in hr_employee.py: {e}")
        return False

def check_views():
    """Check view files"""
    print("🔍 Checking view files...")
    try:
        tree = ET.parse('views/hr_employee_views.xml')
        root = tree.getroot()
        
        # Check for required views
        view_ids = []
        for record in root.findall('.//record[@model="ir.ui.view"]'):
            view_id = record.get('id')
            if view_id:
                view_ids.append(view_id)
        
        required_views = [
            'view_hr_job_description_form',
            'view_hr_job_description_tree',
            'view_employee_form_job_description'
        ]
        
        for view in required_views:
            if view not in view_ids:
                print(f"   ❌ Missing view: {view}")
                return False
        
        print("   ✅ View files are valid")
        return True
    except Exception as e:
        print(f"   ❌ Error in views: {e}")
        return False

def check_security():
    """Check security file"""
    print("🔍 Checking security files...")
    try:
        with open('security/ir.model.access.csv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_access = [
            'access_hr_job_description_user',
            'access_hr_job_description_manager',
            'model_hr_job_description'
        ]
        
        for access in required_access:
            if access not in content:
                print(f"   ❌ Missing access rule: {access}")
                return False
        
        print("   ✅ Security file is valid")
        return True
    except Exception as e:
        print(f"   ❌ Error in security: {e}")
        return False

def check_translation():
    """Check translation file"""
    print("🔍 Checking translation files...")
    try:
        with open('i18n/ar.po', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_translations = [
            'msgid "Job Description"',
            'msgstr "الوصف الوظيفي"',
            'msgid "Job Title"',
            'msgstr "العنوان الوظيفي"'
        ]
        
        for translation in required_translations:
            if translation not in content:
                print(f"   ❌ Missing translation: {translation}")
                return False
        
        print("   ✅ Translation file is valid")
        return True
    except Exception as e:
        print(f"   ❌ Error in translation: {e}")
        return False

def main():
    """Main verification function"""
    print("🎯 HR Job Description Module - Final Verification")
    print("=" * 60)
    
    checks = [
        check_manifest(),
        check_models(),
        check_views(),
        check_security(),
        check_translation()
    ]
    
    print("=" * 60)
    
    if all(checks):
        print("🎉 ALL CHECKS PASSED!")
        print("\n✅ Module is ready for installation!")
        print("\n📋 Installation Steps:")
        print("1. Copy hr_job_description folder to Odoo addons directory")
        print("2. Restart Odoo server")
        print("3. Update Apps List")
        print("4. Install 'HR Job Description' module")
        print("5. Test the new Job Description tab in employee form")
        
        print("\n🌟 Features included:")
        print("• Professional Job Description tab")
        print("• Comprehensive job information fields")
        print("• Arabic/English bilingual support")
        print("• Modern UI following Odoo 15 standards")
        print("• Smart buttons and card layout")
        print("• Proper security and access rights")
        
    else:
        print("❌ SOME CHECKS FAILED!")
        print("Please fix the issues above before installation.")

if __name__ == "__main__":
    main()

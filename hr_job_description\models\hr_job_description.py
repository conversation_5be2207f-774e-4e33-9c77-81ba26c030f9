# -*- coding: utf-8 -*-

from odoo import fields, models, api, _
from datetime import date


class HrJobDescription(models.Model):
    _name = 'hr.job.description'
    _description = 'Employee Job Description'
    _order = 'last_update_date desc'
    _rec_name = 'job_title'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # Standard fields
    name = fields.Char(string='Name', compute='_compute_name', store=True)
    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Basic Information
    employee_id = fields.Many2one(
        'hr.employee',
        string='Employee',
        required=True,
        ondelete='cascade',
        help='Employee for this job description'
    )
    
    job_title = fields.Char(
        string='Job Title',
        required=True,
        help='Official job title for this position',
        tracking=True
    )
    
    department_id = fields.Many2one(
        'hr.department',
        string='Department',
        help='Department where this position belongs',
        tracking=True
    )
    
    # Responsibilities and Requirements
    main_responsibilities = fields.Html(
        string='Main Responsibilities',
        help='Detailed description of main job responsibilities and duties',
        tracking=True
    )
    
    required_qualifications = fields.Html(
        string='Required Qualifications',
        help='Educational qualifications and certifications required for this position',
        tracking=True
    )
    
    skills_experience = fields.Html(
        string='Skills & Experience',
        help='Required skills, competencies, and experience for this position',
        tracking=True
    )
    
    # Reporting Structure
    reports_to = fields.Many2one(
        'hr.employee',
        string='Reports To',
        help='Direct supervisor or manager for this position',
        tracking=True
    )
    
    # Job Level and Location
    job_level = fields.Selection([
        ('entry', 'Entry Level'),
        ('junior', 'Junior'),
        ('mid', 'Mid Level'),
        ('senior', 'Senior'),
        ('lead', 'Team Lead'),
        ('supervisor', 'Supervisor'),
        ('manager', 'Manager'),
        ('senior_manager', 'Senior Manager'),
        ('director', 'Director'),
        ('senior_director', 'Senior Director'),
        ('executive', 'Executive')
    ], string='Job Level',
       help='Professional level classification for this position',
       tracking=True)
    
    work_location = fields.Char(
        string='Work Location',
        help='Primary work location for this position',
        tracking=True
    )
    
    # Dates and Status
    last_update_date = fields.Date(
        string='Last Update Date',
        default=fields.Date.context_today,
        help='Date when this job description was last updated',
        tracking=True
    )

    effective_date = fields.Date(
        string='Effective Date',
        default=fields.Date.context_today,
        help='Date when this job description becomes effective',
        tracking=True
    )


    
    # Additional Information
    additional_notes = fields.Text(
        string='Additional Notes',
        help='Any additional notes or special requirements for this position',
        tracking=True
    )
    
    # Company
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        help='Company for this job description'
    )

    @api.depends('job_title', 'employee_id')
    def _compute_name(self):
        """Compute name field"""
        for record in self:
            if record.employee_id and record.job_title:
                record.name = f"{record.employee_id.name} - {record.job_title}"
            elif record.job_title:
                record.name = record.job_title
            else:
                record.name = "Job Description"

    @api.model
    def create(self, vals):
        """Set last update date on creation"""
        vals['last_update_date'] = fields.Date.context_today(self)
        return super(HrJobDescription, self).create(vals)

    def write(self, vals):
        """Update last update date on modification"""
        vals['last_update_date'] = fields.Date.context_today(self)
        return super(HrJobDescription, self).write(vals)

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Auto-populate fields when employee changes"""
        if self.employee_id:
            self.job_title = self.employee_id.job_title or self.employee_id.job_id.name
            self.department_id = self.employee_id.department_id.id
            self.reports_to = self.employee_id.parent_id.id
            self.work_location = self.employee_id.work_location_id.name if self.employee_id.work_location_id else ''

    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"{record.job_title}"
            if record.employee_id:
                name = f"{record.employee_id.name} - {record.job_title}"
            result.append((record.id, name))
        return result

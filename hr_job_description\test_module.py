#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test script to verify the module structure
"""

import os
import sys

def test_module_structure():
    """Test if all required files exist"""
    base_path = os.path.dirname(os.path.abspath(__file__))
    
    required_files = [
        '__manifest__.py',
        '__init__.py',
        'models/__init__.py',
        'models/hr_employee.py',
        'models/hr_job_description.py',
        'views/hr_employee_views.xml',
        'security/ir.model.access.csv',
        'i18n/ar.po',
        'demo/hr_job_description_demo.xml',
        'README.md',
        'INSTALLATION.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files exist!")
        return True

def test_manifest():
    """Test manifest file"""
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for required keys
        required_keys = ['name', 'version', 'depends', 'data']
        for key in required_keys:
            if f"'{key}'" not in content:
                print(f"❌ Missing key in manifest: {key}")
                return False
                
        print("✅ Manifest file looks good!")
        return True
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 Testing HR Job Description Module...")
    print("=" * 50)
    
    # Test module structure
    structure_ok = test_module_structure()
    
    # Test manifest
    manifest_ok = test_manifest()
    
    print("=" * 50)
    if structure_ok and manifest_ok:
        print("🎉 Module structure test PASSED!")
        print("\n📋 Next steps:")
        print("1. Copy the module to your Odoo addons directory")
        print("2. Restart Odoo server")
        print("3. Update Apps List")
        print("4. Install 'HR Job Description' module")
        print("5. Go to HR > Employees and check the new tab")
    else:
        print("❌ Module structure test FAILED!")
        print("Please fix the issues above before installing.")

if __name__ == "__main__":
    main()

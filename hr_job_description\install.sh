#!/bin/bash

# HR Job Description Module Installation Script
# This script helps install the module in Odoo

echo "🚀 HR Job Description Module Installation"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "__manifest__.py" ]; then
    echo "❌ Error: Please run this script from the hr_job_description directory"
    exit 1
fi

# Function to copy module to Odoo addons
install_module() {
    local odoo_path="$1"
    local addons_path="$odoo_path/addons"
    
    if [ ! -d "$addons_path" ]; then
        echo "❌ Error: Odoo addons directory not found at $addons_path"
        return 1
    fi
    
    echo "📁 Copying module to $addons_path/hr_job_description..."
    
    # Create destination directory
    mkdir -p "$addons_path/hr_job_description"
    
    # Copy all files
    cp -r * "$addons_path/hr_job_description/"
    
    echo "✅ Module copied successfully!"
    return 0
}

# Check for common Odoo installation paths
ODOO_PATHS=(
    "/opt/odoo"
    "/usr/lib/python3/dist-packages/odoo"
    "/odoo"
    "/var/lib/odoo"
)

echo "🔍 Looking for Odoo installation..."

ODOO_FOUND=false
for path in "${ODOO_PATHS[@]}"; do
    if [ -d "$path/addons" ]; then
        echo "✅ Found Odoo at: $path"
        read -p "📥 Install module to $path/addons? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if install_module "$path"; then
                ODOO_FOUND=true
                break
            fi
        fi
    fi
done

if [ "$ODOO_FOUND" = false ]; then
    echo "❓ Odoo installation not found automatically."
    read -p "📂 Please enter your Odoo installation path: " CUSTOM_PATH
    if [ -n "$CUSTOM_PATH" ] && [ -d "$CUSTOM_PATH" ]; then
        install_module "$CUSTOM_PATH"
    else
        echo "❌ Invalid path provided."
        echo "📋 Manual installation steps:"
        echo "1. Copy the hr_job_description folder to your Odoo addons directory"
        echo "2. Restart Odoo server"
        echo "3. Update Apps List in Odoo"
        echo "4. Install 'HR Job Description' module"
        exit 1
    fi
fi

echo ""
echo "🎉 Installation completed!"
echo ""
echo "📋 Next steps:"
echo "1. Restart your Odoo server"
echo "2. Login to Odoo as administrator"
echo "3. Go to Apps > Update Apps List"
echo "4. Search for 'HR Job Description'"
echo "5. Click Install"
echo "6. Go to HR > Employees to see the new Job Description tab"
echo ""
echo "📚 For more information, check README.md and INSTALLATION.md"

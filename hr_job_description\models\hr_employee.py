# -*- coding: utf-8 -*-

from odoo import fields, models, api, _


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # Job Description relationship
    job_description_ids = fields.One2many(
        'hr.job.description',
        'employee_id',
        string='Job Descriptions',
        help='Job descriptions for this employee'
    )
    
    current_job_description_id = fields.Many2one(
        'hr.job.description',
        string='Current Job Description',
        compute='_compute_current_job_description',
        store=True,
        help='Current active job description for this employee'
    )
    
    job_description_count = fields.Integer(
        string='Job Description Count',
        compute='_compute_job_description_count',
        help='Number of job descriptions for this employee'
    )

    # Display fields for job description content
    current_job_title_display = fields.Char(
        string='Current Job Title',
        compute='_compute_job_description_display',
        help='Current job title from active job description'
    )

    current_department_display = fields.Char(
        string='Department',
        compute='_compute_job_description_display',
        help='Department from current job description'
    )

    current_job_level_display = fields.Char(
        string='Job Level',
        compute='_compute_job_description_display',
        help='Job level from current job description'
    )

    current_work_location_display = fields.Char(
        string='Work Location',
        compute='_compute_job_description_display',
        help='Work location from current job description'
    )

    current_reports_to_display = fields.Char(
        string='Reports To',
        compute='_compute_job_description_display',
        help='Reports to from current job description'
    )

    current_responsibilities_display = fields.Html(
        string='Main Responsibilities',
        compute='_compute_job_description_display',
        help='Main responsibilities from current job description'
    )

    current_qualifications_display = fields.Html(
        string='Required Qualifications',
        compute='_compute_job_description_display',
        help='Required qualifications from current job description'
    )

    current_skills_display = fields.Html(
        string='Skills & Experience',
        compute='_compute_job_description_display',
        help='Skills and experience from current job description'
    )

    current_additional_notes_display = fields.Text(
        string='Additional Notes',
        compute='_compute_job_description_display',
        help='Additional notes from current job description'
    )

    @api.depends('job_description_ids', 'job_description_ids.active', 'job_description_ids.effective_date')
    def _compute_current_job_description(self):
        """Compute current active job description"""
        for employee in self:
            current_desc = employee.job_description_ids.filtered(
                lambda x: x.active and x.effective_date <= fields.Date.today()
            ).sorted('effective_date', reverse=True)
            employee.current_job_description_id = current_desc[0] if current_desc else False

    @api.depends('job_description_ids')
    def _compute_job_description_count(self):
        """Compute job description count"""
        for employee in self:
            employee.job_description_count = len(employee.job_description_ids)

    @api.depends('current_job_description_id', 'current_job_description_id.job_title',
                 'current_job_description_id.department_id', 'current_job_description_id.job_level',
                 'current_job_description_id.work_location', 'current_job_description_id.reports_to',
                 'current_job_description_id.main_responsibilities', 'current_job_description_id.required_qualifications',
                 'current_job_description_id.skills_experience', 'current_job_description_id.additional_notes')
    def _compute_job_description_display(self):
        """Compute job description display fields"""
        for employee in self:
            if employee.current_job_description_id:
                jd = employee.current_job_description_id
                employee.current_job_title_display = jd.job_title or ''
                employee.current_department_display = jd.department_id.name if jd.department_id else ''
                employee.current_job_level_display = dict(jd._fields['job_level'].selection).get(jd.job_level, '') if jd.job_level else ''
                employee.current_work_location_display = jd.work_location or ''
                employee.current_reports_to_display = jd.reports_to.name if jd.reports_to else ''
                employee.current_responsibilities_display = jd.main_responsibilities or ''
                employee.current_qualifications_display = jd.required_qualifications or ''
                employee.current_skills_display = jd.skills_experience or ''
                employee.current_additional_notes_display = jd.additional_notes or ''
            else:
                employee.current_job_title_display = ''
                employee.current_department_display = ''
                employee.current_job_level_display = ''
                employee.current_work_location_display = ''
                employee.current_reports_to_display = ''
                employee.current_responsibilities_display = ''
                employee.current_qualifications_display = ''
                employee.current_skills_display = ''
                employee.current_additional_notes_display = ''

    def action_view_job_descriptions(self):
        """Action to view job descriptions"""
        self.ensure_one()
        action = self.env.ref('hr_job_description.action_hr_job_description').read()[0]
        action['domain'] = [('employee_id', '=', self.id)]
        action['context'] = {
            'default_employee_id': self.id,
            'default_job_title': self.job_title or self.job_id.name,
            'default_department_id': self.department_id.id,
            'default_reports_to': self.parent_id.id,
        }
        if len(self.job_description_ids) == 1:
            action['views'] = [(False, 'form')]
            action['res_id'] = self.job_description_ids.id
        return action

    def create_job_description(self):
        """Create new job description for this employee"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Create Job Description'),
            'res_model': 'hr.job.description',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_employee_id': self.id,
                'default_job_title': self.job_title or self.job_id.name,
                'default_department_id': self.department_id.id,
                'default_reports_to': self.parent_id.id,
                'default_work_location': self.work_location_id.name if self.work_location_id else '',
            }
        }
